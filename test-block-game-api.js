// Test the actual block game API endpoint
const axios = require('axios');

const BASE_URL = 'http://localhost:3012';

// Test payload from the logs
const testPayload = {
  "block_game_id": "53b3c0fb-769e-493f-b7df-b2c231ed12e5",
  "sentence_constructions": [
    {
      "starting_sentence": "The fan makes a",
      "expanding_sentence": "strange sound",
      "sentence_order": 1
    },
    {
      "starting_sentence": "I will check",
      "expanding_sentence": "it tomorrow morning",
      "sentence_order": 2
    }
  ]
};

async function testBlockGameSubmission() {
  try {
    console.log('Testing block game submission...');
    console.log('Payload:', JSON.stringify(testPayload, null, 2));
    
    // Note: This will fail with authentication error, but we want to see if the database error is fixed
    const response = await axios.post(`${BASE_URL}/play/block/submit`, testPayload, {
      headers: {
        'Content-Type': 'application/json',
        // No auth token - will get 401, but that's expected
      }
    });
    
    console.log('Unexpected success! Response:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('Response Status:', error.response.status);
      console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
      
      // Check if we get authentication error (expected) vs database error (fixed)
      if (error.response.status === 401) {
        console.log('\n✓ Got authentication error (expected) - database issue is fixed!');
      } else if (error.response.status === 400 && 
                 error.response.data.message && 
                 error.response.data.message.includes('score')) {
        console.log('\n✗ Still getting database score column error');
      } else {
        console.log('\n? Got different error - check details above');
      }
    } else {
      console.log('Network error:', error.message);
    }
  }
}

testBlockGameSubmission();
